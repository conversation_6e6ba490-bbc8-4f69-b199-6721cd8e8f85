import {
  Configuration,
  AdaptivePlaywrightCrawler,
  log,
} from "crawlee";

import { launchOptions } from 'camoufox-js';
import { firefox } from 'playwright';

import { getOrInitQueueAdapter } from "./src/queue-adapter.js";
import { sites } from "./src/sites/index.js";

async function testCrawlerSimple() {
  console.log("🔍 Testing simple crawler with queue adapter...");
  
  try {
    // Disable purge on start
    Configuration.set("purgeOnStart", false);

    // Use shared storage
    const config = new Configuration({
      storageClientOptions: {
        localDataDirectory: './storage/shared',
      },
    });

    // Get queue adapter
    const requestQueue = await getOrInitQueueAdapter(true);
    console.log("✅ Queue adapter initialized");
    
    // Add a test request
    const testSite = sites.find(s => s.enabled);
    if (testSite) {
      await requestQueue.addRequest({
        url: `${testSite.baseUrl}${testSite.startPath}`,
        userData: { site: testSite },
        label: "MANGA_LIST",
      });
      console.log("✅ Test request added");
    }

    // Simple request handler
    const requestHandler = async ({ request, log }) => {
      log.info(`🔎 Processing: ${request.url}`);
      console.log("✅ Request processed successfully");
    };

    // Create crawler
    const crawler = new AdaptivePlaywrightCrawler(
      {
        launchContext: {
          launcher: firefox,
          launchOptions: await launchOptions({
            headless: true,
          }),
        },
        log,
        requestHandler,
        requestQueue,
        maxConcurrency: 1,
        maxRequestRetries: 1,
        requestHandlerTimeoutSecs: 30,
        navigationTimeoutSecs: 15,
      },
      config
    );

    console.log("🚀 Starting crawler...");
    await crawler.run();
    console.log("✅ Crawler completed successfully");
    
  } catch (error) {
    console.error("❌ Error:", error);
    console.error("Stack:", error.stack);
  }
}

testCrawlerSimple().then(() => {
  console.log("🎉 Simple crawler test complete");
  process.exit(0);
}).catch(error => {
  console.error("💥 Simple crawler test failed:", error);
  console.error("Stack:", error.stack);
  process.exit(1);
});
