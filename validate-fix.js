#!/usr/bin/env node

/**
 * Simple validation script to check if the MANGA_DETAIL fixes are applied correctly
 */

import fs from 'fs';
import path from 'path';

console.log("🔍 Validating MANGA_DETAIL Fixes\n");
console.log("================================");

function checkFile(filePath, checks) {
  console.log(`\n📁 Checking ${filePath}:`);
  
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    let allPassed = true;
    
    checks.forEach(({ name, test, expected }) => {
      const result = test(content);
      const passed = result === expected;
      console.log(`   ${name}: ${passed ? '✅ PASS' : '❌ FAIL'}`);
      if (!passed) {
        allPassed = false;
      }
    });
    
    return allPassed;
  } catch (error) {
    console.log(`   ❌ Error reading file: ${error.message}`);
    return false;
  }
}

// Validation checks
const mangaListChecks = [
  {
    name: "getOrInitQueue import removed",
    test: (content) => !content.includes('import { getOrInitQueue }'),
    expected: true
  },
  {
    name: "requestQueue parameter removed from enqueueLinks",
    test: (content) => !content.includes('requestQueue: await getOrInitQueue()'),
    expected: true
  },
  {
    name: "MANGA_DETAIL label present",
    test: (content) => content.includes('label: "MANGA_DETAIL"'),
    expected: true
  },
  {
    name: "Enhanced logging added",
    test: (content) => content.includes('📚 Found') && content.includes('manga links on current page'),
    expected: true
  },
  {
    name: "Debug logging for enqueuing",
    test: (content) => content.includes('🔗 Enqueuing MANGA_DETAIL'),
    expected: true
  }
];

const mangaDetailChecks = [
  {
    name: "getOrInitQueue import removed",
    test: (content) => !content.includes('import { getOrInitQueue }'),
    expected: true
  },
  {
    name: "requestQueue parameter removed from enqueueLinks",
    test: (content) => !content.includes('requestQueue: await getOrInitQueue()'),
    expected: true
  },
  {
    name: "CHAPTER_DETAIL label present",
    test: (content) => content.includes('label: "CHAPTER_DETAIL"'),
    expected: true
  },
  {
    name: "Enhanced logging added",
    test: (content) => content.includes('📖 Found') && content.includes('chapter links'),
    expected: true
  }
];

// Run validations
const mangaListOk = checkFile('src/routes/manga-list-route.js', mangaListChecks);
const mangaDetailOk = checkFile('src/routes/manga-detail-route.js', mangaDetailChecks);

// Check router configuration
console.log(`\n📁 Checking src/routes/index.js:`);
try {
  const routerContent = fs.readFileSync('src/routes/index.js', 'utf8');
  const hasMangaDetailHandler = routerContent.includes('router.addHandler("MANGA_DETAIL", handleMangaDetail)');
  console.log(`   MANGA_DETAIL handler registered: ${hasMangaDetailHandler ? '✅ PASS' : '❌ FAIL'}`);
} catch (error) {
  console.log(`   ❌ Error reading router file: ${error.message}`);
}

// Check site configuration
console.log(`\n📁 Checking site configurations:`);
try {
  const sitesModule = await import('./src/sites/index.js');
  const sites = sitesModule.sites;
  const enabledSites = sites.filter(site => site.enabled);
  
  console.log(`   Total sites: ${sites.length}`);
  console.log(`   Enabled sites: ${enabledSites.length}`);
  
  if (enabledSites.length > 0) {
    const testSite = enabledSites[0];
    console.log(`   Test site: ${testSite.name} ✅`);
    
    const hasSelectors = testSite.selectors && 
                        testSite.selectors.listManga && 
                        testSite.selectors.listManga.mangaLink;
    console.log(`   Required selectors: ${hasSelectors ? '✅ PASS' : '❌ FAIL'}`);
  } else {
    console.log(`   ⚠️  No enabled sites found - you'll need to enable a site to test`);
  }
} catch (error) {
  console.log(`   ❌ Error checking sites: ${error.message}`);
}

// Summary
console.log(`\n📊 Validation Summary:`);
console.log(`=====================`);
console.log(`manga-list-route.js: ${mangaListOk ? '✅ PASS' : '❌ FAIL'}`);
console.log(`manga-detail-route.js: ${mangaDetailOk ? '✅ PASS' : '❌ FAIL'}`);

if (mangaListOk && mangaDetailOk) {
  console.log(`\n🎉 All validations passed!`);
  console.log(`\nThe MANGA_DETAIL queue consistency issue has been fixed.`);
  console.log(`\n💡 To test the actual crawling:`);
  console.log(`   1. Make sure at least one site is enabled in src/sites/`);
  console.log(`   2. Run: npm start`);
  console.log(`   3. Look for these log messages:`);
  console.log(`      - "📚 Found X manga links on current page"`);
  console.log(`      - "👉 Scraping detail page [URL]"`);
  console.log(`      - "✅ Scraped [MANGA_TITLE]"`);
} else {
  console.log(`\n❌ Some validations failed. Please review the issues above.`);
}
