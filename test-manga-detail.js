#!/usr/bin/env node

/**
 * Test script to verify MANGA_DETAIL functionality
 * This script helps debug queue and request processing issues
 */

import { router } from "./src/routes/index.js";
import { sites } from "./src/sites/index.js";

console.log("🧪 Testing MANGA_DETAIL Route Configuration...\n");

function testRouterConfiguration() {
  console.log("1. 🔍 Checking router configuration...");

  // Check if router has the required handlers
  const routerHandlers = router._routes || router.routes || {};
  console.log("   Router handlers found:", Object.keys(routerHandlers));

  // Check if MANGA_DETAIL handler exists
  const hasMangaDetailHandler = router._routes && router._routes.has && router._routes.has("MANGA_DETAIL");
  console.log("   MANGA_DETAIL handler registered:", hasMangaDetailHandler ? "✅ YES" : "❌ NO");

  return hasMangaDetailHandler;
}

function testSiteConfiguration() {
  console.log("\n2. 🌐 Checking site configurations...");

  const enabledSites = sites.filter(site => site.enabled);
  console.log(`   Total sites: ${sites.length}`);
  console.log(`   Enabled sites: ${enabledSites.length}`);

  if (enabledSites.length === 0) {
    console.log("   ❌ No enabled sites found!");
    return false;
  }

  const testSite = enabledSites[0];
  console.log(`   Testing with: ${testSite.name}`);
  console.log(`   Base URL: ${testSite.baseUrl}`);
  console.log(`   Start path: ${testSite.startPath}`);

  // Check selectors
  const hasRequiredSelectors = testSite.selectors &&
                               testSite.selectors.listManga &&
                               testSite.selectors.listManga.mangaLink &&
                               testSite.selectors.detailManga;

  console.log("   Required selectors present:", hasRequiredSelectors ? "✅ YES" : "❌ NO");

  if (hasRequiredSelectors) {
    console.log(`   Manga link selector: ${testSite.selectors.listManga.mangaLink}`);
    console.log(`   Detail selectors configured: ✅ YES`);
  }

  return hasRequiredSelectors;
}

function testQueueConsistency() {
  console.log("\n3. 🔄 Checking queue consistency fix...");

  // Read the manga-list-route.js file to verify the fix
  try {
    const fs = await import('fs');
    const mangaListRoute = fs.readFileSync('./src/routes/manga-list-route.js', 'utf8');

    // Check if the problematic requestQueue parameter was removed
    const hasProblematicQueue = mangaListRoute.includes('requestQueue: await getOrInitQueue()');
    const hasGetOrInitQueueImport = mangaListRoute.includes('import { getOrInitQueue }');

    console.log("   Problematic queue usage removed:", !hasProblematicQueue ? "✅ YES" : "❌ NO");
    console.log("   getOrInitQueue import removed:", !hasGetOrInitQueueImport ? "✅ YES" : "❌ NO");

    return !hasProblematicQueue && !hasGetOrInitQueueImport;
  } catch (error) {
    console.log("   ❌ Could not verify queue fix:", error.message);
    return false;
  }
}

async function runTests() {
  console.log("🧪 MANGA_DETAIL Functionality Test\n");
  console.log("===================================");

  const routerOk = testRouterConfiguration();
  const siteOk = testSiteConfiguration();
  const queueOk = await testQueueConsistency();

  console.log("\n📊 Test Summary:");
  console.log("================");
  console.log(`Router Configuration: ${routerOk ? "✅ PASS" : "❌ FAIL"}`);
  console.log(`Site Configuration: ${siteOk ? "✅ PASS" : "❌ FAIL"}`);
  console.log(`Queue Consistency Fix: ${queueOk ? "✅ PASS" : "❌ FAIL"}`);

  if (routerOk && siteOk && queueOk) {
    console.log("\n🎉 All tests passed! The MANGA_DETAIL functionality should work correctly.");
    console.log("\n💡 To test with actual crawling, run your main crawler:");
    console.log("   npm start");
    console.log("\n   Look for these log messages:");
    console.log("   - '📚 Found X manga links on current page'");
    console.log("   - '👉 Scraping detail page [URL]'");
    console.log("   - '✅ Scraped [MANGA_TITLE]'");
  } else {
    console.log("\n❌ Some tests failed. Please review the issues above.");
  }
}

// Run the tests
runTests().catch(console.error);
