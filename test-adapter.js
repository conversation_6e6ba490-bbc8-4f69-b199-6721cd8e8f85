import { getOrInitQueueAdapter } from "./src/queue-adapter.js";
import { sites } from "./src/sites/index.js";

async function testAdapter() {
  console.log("🔍 Testing queue adapter...");
  
  try {
    // Initialize fresh queue adapter
    const queueAdapter = await getOrInitQueueAdapter(true);
    console.log("✅ Queue adapter initialized");
    console.log("  - Name:", queueAdapter.name);
    console.log("  - ID:", queueAdapter.id);
    
    // Test isEmpty
    const isEmpty = await queueAdapter.isEmpty();
    console.log("📊 Initial isEmpty:", isEmpty);
    
    // Test size
    const size = await queueAdapter.size();
    console.log("📊 Initial size:", size);
    
    // Add a test request
    const testSite = sites.find(s => s.enabled);
    if (testSite) {
      console.log(`🌱 Adding test request for site: ${testSite.name}`);
      
      await queueAdapter.addRequest({
        url: `${testSite.baseUrl}${testSite.startPath}`,
        userData: { site: testSite },
        label: "MANGA_LIST",
      });
      
      console.log("✅ Test request added");
      
      // Check size after adding
      const newSize = await queueAdapter.size();
      const newIsEmpty = await queueAdapter.isEmpty();
      console.log("📊 After adding - size:", newSize, "isEmpty:", newIsEmpty);
      
      // Try to fetch the request
      console.log("🔄 Trying to fetch request...");
      const request = await queueAdapter.fetchNextRequest();
      console.log("📥 Fetched request:", request ? {
        id: request.id,
        url: request.url,
        label: request.label
      } : null);
      
      if (request) {
        // Mark as handled
        await queueAdapter.markRequestHandled(request);
        console.log("✅ Request marked as handled");
        
        // Final stats
        const finalSize = await queueAdapter.size();
        const finalIsEmpty = await queueAdapter.isEmpty();
        console.log("📊 Final - size:", finalSize, "isEmpty:", finalIsEmpty);
      }
    }
    
  } catch (error) {
    console.error("❌ Error:", error);
    console.error("Stack:", error.stack);
  }
}

testAdapter().then(() => {
  console.log("🎉 Adapter test complete");
  process.exit(0);
}).catch(error => {
  console.error("💥 Adapter test failed:", error);
  console.error("Stack:", error.stack);
  process.exit(1);
});
