import { getOrInitQueue, getQueueStats } from "./src/shared.js";
import { sites } from "./src/sites/index.js";

async function debugQueue() {
  console.log("🔍 Debugging queue...");
  
  try {
    // Initialize fresh queue
    const queue = await getOrInitQueue(true);
    console.log("✅ Queue initialized");
    
    // Get initial stats
    let stats = await getQueueStats();
    console.log("📊 Initial stats:", stats);
    
    // Add a test request
    const testSite = sites.find(s => s.enabled);
    if (testSite) {
      console.log(`🌱 Adding test request for site: ${testSite.name}`);
      
      await queue.addRequest({
        url: `${testSite.baseUrl}${testSite.startPath}`,
        userData: { site: testSite },
        label: "MANGA_LIST",
      });
      
      console.log("✅ Test request added");
      
      // Get stats after adding
      stats = await getQueueStats();
      console.log("📊 Stats after adding:", stats);

      // Debug queue internals
      console.log("🔍 Queue internals:");
      console.log("  - queue.name:", queue.name);
      console.log("  - queue.id:", queue.id);
      console.log("  - typeof queue.size:", typeof queue.size);
      console.log("  - typeof queue.length:", typeof queue.length);
      console.log("  - typeof queue.isEmpty:", typeof queue.isEmpty);

      // Try different size methods
      try {
        if (typeof queue.size === 'function') {
          const size = await queue.size();
          console.log("  - queue.size():", size);
        }
      } catch (e) {
        console.log("  - queue.size() error:", e.message);
      }

      try {
        if (typeof queue.length === 'function') {
          const length = await queue.length();
          console.log("  - queue.length():", length);
        }
      } catch (e) {
        console.log("  - queue.length() error:", e.message);
      }

      try {
        if (typeof queue.isEmpty === 'function') {
          const isEmpty = await queue.isEmpty();
          console.log("  - queue.isEmpty():", isEmpty);
        }
      } catch (e) {
        console.log("  - queue.isEmpty() error:", e.message);
      }
      
      // Try to fetch the request
      console.log("🔄 Trying to fetch request...");
      const request = await queue.fetchNextRequest();
      console.log("📥 Fetched request:", request ? {
        id: request.id,
        url: request.url,
        label: request.label
      } : null);
      
      if (request) {
        // Mark as handled
        await queue.markRequestHandled(request);
        console.log("✅ Request marked as handled");
        
        // Final stats
        stats = await getQueueStats();
        console.log("📊 Final stats:", stats);
      }
    }
    
  } catch (error) {
    console.error("❌ Error:", error);
  }
}

debugQueue().then(() => {
  console.log("🎉 Debug complete");
  process.exit(0);
}).catch(error => {
  console.error("💥 Debug failed:", error);
  process.exit(1);
});
