import { getOrInitSimpleQueue, getSimpleQueueStats } from "./src/simple-queue.js";
import { sites } from "./src/sites/index.js";

async function testSimpleQueue() {
  console.log("🔍 Testing simple queue...");
  
  try {
    // Initialize fresh queue
    const queue = await getOrInitSimpleQueue(true);
    console.log("✅ Simple queue initialized");
    
    // Get initial stats
    let stats = await getSimpleQueueStats();
    console.log("📊 Initial stats:", stats);
    
    // Add a test request
    const testSite = sites.find(s => s.enabled);
    if (testSite) {
      console.log(`🌱 Adding test request for site: ${testSite.name}`);
      
      await queue.addRequest({
        url: `${testSite.baseUrl}${testSite.startPath}`,
        userData: { site: testSite },
        label: "MANGA_LIST",
      });
      
      console.log("✅ Test request added");
      
      // Get stats after adding
      stats = await getSimpleQueueStats();
      console.log("📊 Stats after adding:", stats);
      
      // Try to fetch the request
      console.log("🔄 Trying to fetch request...");
      const request = await queue.fetchNextRequest();
      console.log("📥 Fetched request:", request ? {
        id: request.id,
        url: request.url,
        label: request.label
      } : null);
      
      if (request) {
        // Mark as handled
        await queue.markRequestHandled(request);
        console.log("✅ Request marked as handled");
        
        // Final stats
        stats = await getSimpleQueueStats();
        console.log("📊 Final stats:", stats);
      }
    }
    
  } catch (error) {
    console.error("❌ Error:", error);
  }
}

testSimpleQueue().then(() => {
  console.log("🎉 Simple queue test complete");
  process.exit(0);
}).catch(error => {
  console.error("💥 Simple queue test failed:", error);
  process.exit(1);
});
