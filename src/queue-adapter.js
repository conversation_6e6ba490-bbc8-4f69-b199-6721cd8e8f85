import { getOrInitSimpleQueue } from "./simple-queue.js";

/**
 * Adapter to make SimpleQueue compatible with Crawlee's RequestQueue interface
 */
export class RequestQueueAdapter {
  constructor(simpleQueue) {
    this.simpleQueue = simpleQueue;
    this.name = simpleQueue.name;
    this.id = `adapter-${simpleQueue.name}`;
  }

  /**
   * Add request to queue
   */
  async addRequest(request, options = {}) {
    return await this.simpleQueue.addRequest(request);
  }

  /**
   * Add multiple requests to queue
   */
  async addRequests(requests, options = {}) {
    return await this.simpleQueue.addRequests(requests);
  }

  /**
   * Fetch next request from queue
   */
  async fetchNextRequest() {
    return await this.simpleQueue.fetchNextRequest();
  }

  /**
   * Mark request as handled
   */
  async markRequestHandled(request) {
    return await this.simpleQueue.markRequestHandled(request);
  }

  /**
   * Reclaim request (put it back in queue)
   */
  async reclaimRequest(request, options = {}) {
    // For simple queue, we just add it back
    return await this.simpleQueue.addRequest(request);
  }

  /**
   * Check if queue is empty
   */
  async isEmpty() {
    return await this.simpleQueue.isEmpty();
  }

  /**
   * Get queue size (not available in standard RequestQueue, but useful)
   */
  async size() {
    return await this.simpleQueue.size();
  }

  /**
   * Get handled request count (required by crawler)
   */
  async handledCount() {
    // Simple queue doesn't track handled count, return 0
    return 0;
  }

  /**
   * Get total request count (required by crawler)
   */
  async totalCount() {
    // For simple queue, total count is current size + handled (which we don't track)
    return await this.simpleQueue.size();
  }

  /**
   * Check if queue is finished (required by crawler)
   */
  async isFinished() {
    // Queue is finished when it's empty
    return await this.simpleQueue.isEmpty();
  }

  /**
   * Drop/clear the queue
   */
  async drop() {
    return await this.simpleQueue.drop();
  }

  /**
   * Get request by ID (not implemented for simple queue)
   */
  async getRequest(id) {
    throw new Error("getRequest not implemented for SimpleQueue adapter");
  }

  /**
   * Update request (not implemented for simple queue)
   */
  async updateRequest(request, options = {}) {
    throw new Error("updateRequest not implemented for SimpleQueue adapter");
  }

  /**
   * Delete request (not implemented for simple queue)
   */
  async deleteRequest(id) {
    throw new Error("deleteRequest not implemented for SimpleQueue adapter");
  }

  /**
   * Get info about the queue
   */
  async getInfo() {
    const size = await this.simpleQueue.size();
    const isEmpty = await this.simpleQueue.isEmpty();
    
    return {
      id: this.id,
      name: this.name,
      totalRequestCount: size,
      handledRequestCount: 0, // Not tracked in simple queue
      pendingRequestCount: size,
      createdAt: new Date(), // Not tracked in simple queue
      modifiedAt: new Date(), // Not tracked in simple queue
      accessedAt: new Date(),
    };
  }

  /**
   * Static method to create adapter from simple queue
   */
  static async create(name, options = {}) {
    const simpleQueue = await getOrInitSimpleQueue(options.makeFresh || false);
    return new RequestQueueAdapter(simpleQueue);
  }
}

/**
 * Get or create request queue adapter
 */
export async function getOrInitQueueAdapter(makeFresh = false) {
  return await RequestQueueAdapter.create("manga-crawler-queue", { makeFresh });
}
