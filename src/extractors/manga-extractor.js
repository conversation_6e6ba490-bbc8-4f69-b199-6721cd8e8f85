import { htmlToText } from "crawlee";
import slugify from "slugify";

export function mangaDetail($, mangaDetailSelectors) {
  const title = $(mangaDetailSelectors.title).text().trim();
  const slug = slugify(title, { lower: true });
  const description = htmlToText($(mangaDetailSelectors.description));

  const detail = {
    title,
    slug,
    description,
    coverImage: $(mangaDetailSelectors.cover).attr("src") || "",
    status: $(mangaDetailSelectors.status).text().trim() || "",
    authors: $(mangaDetailSelectors.authors)
      .map((_, author) => $(author).text().trim())
      .get(),
    genres: $(mangaDetailSelectors.genres)
      .map((_, genre) => $(genre).text().trim())
      .get(),
  };

  return detail;
}
