import { writeFileSync, readFileSync, existsSync, mkdirSync } from 'fs';
import { join, dirname } from 'path';

// Simple logger
const log = {
  info: (msg, ...args) => console.log(`INFO  ${msg}`, ...args),
  warn: (msg, ...args) => console.warn(`WARN  ${msg}`, ...args),
  error: (msg, ...args) => console.error(`ERROR ${msg}`, ...args),
  debug: (msg, ...args) => console.log(`DEBUG ${msg}`, ...args),
};

/**
 * Simple file-based queue for sharing requests between workers
 */
class SimpleQueue {
  constructor(name, storageDir = './storage/shared') {
    this.name = name;
    this.storageDir = storageDir;
    this.queueFile = join(storageDir, 'queues', `${name}.json`);
    this.lockFile = join(storageDir, 'queues', `${name}.lock`);
    
    // Ensure directory exists
    mkdirSync(dirname(this.queueFile), { recursive: true });
    
    // Initialize queue file if it doesn't exist
    if (!existsSync(this.queueFile)) {
      this._saveQueue([]);
    }
  }

  /**
   * Load queue from file
   */
  _loadQueue() {
    try {
      if (existsSync(this.queueFile)) {
        const data = readFileSync(this.queueFile, 'utf8');
        return JSON.parse(data);
      }
    } catch (error) {
      log.warn(`Failed to load queue ${this.name}:`, error.message);
    }
    return [];
  }

  /**
   * Save queue to file
   */
  _saveQueue(queue) {
    try {
      writeFileSync(this.queueFile, JSON.stringify(queue, null, 2));
    } catch (error) {
      log.error(`Failed to save queue ${this.name}:`, error.message);
      throw error;
    }
  }

  /**
   * Simple lock mechanism using try-catch
   */
  _withLock(operation) {
    const maxRetries = 5;

    for (let i = 0; i < maxRetries; i++) {
      try {
        return operation();
      } catch (error) {
        if (i === maxRetries - 1) {
          throw error;
        }
        // Wait a bit and retry
        const delay = Math.random() * 100 + 50; // 50-150ms
        require('child_process').execSync(`sleep ${delay / 1000}`);
      }
    }
  }

  /**
   * Add request to queue
   */
  async addRequest(request) {
    return this._withLock(() => {
      const queue = this._loadQueue();

      // Add unique ID if not present
      if (!request.id) {
        request.id = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      }

      // Add timestamp
      request.addedAt = new Date().toISOString();

      queue.push(request);
      this._saveQueue(queue);

      log.debug(`Added request to queue ${this.name}: ${request.url}`);
    });
  }

  /**
   * Add multiple requests to queue
   */
  async addRequests(requests) {
    if (!Array.isArray(requests)) {
      requests = [requests];
    }

    return this._withLock(() => {
      const queue = this._loadQueue();

      for (const request of requests) {
        // Add unique ID if not present
        if (!request.id) {
          request.id = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        }

        // Add timestamp
        request.addedAt = new Date().toISOString();

        queue.push(request);
      }

      this._saveQueue(queue);

      log.debug(`Added ${requests.length} requests to queue ${this.name}`);
    });
  }

  /**
   * Fetch next request from queue
   */
  async fetchNextRequest() {
    return this._withLock(() => {
      const queue = this._loadQueue();

      if (queue.length === 0) {
        return null;
      }

      // Get first request
      const request = queue.shift();
      this._saveQueue(queue);

      log.debug(`Fetched request from queue ${this.name}: ${request.url}`);
      return request;
    });
  }

  /**
   * Check if queue is empty
   */
  async isEmpty() {
    const queue = this._loadQueue();
    return queue.length === 0;
  }

  /**
   * Get queue size
   */
  async size() {
    const queue = this._loadQueue();
    return queue.length;
  }

  /**
   * Get total count (alias for size for Crawlee compatibility)
   */
  async getTotalCount() {
    return this.size();
  }

  /**
   * Drop/clear the queue
   */
  async drop() {
    return this._withLock(() => {
      this._saveQueue([]);
      log.info(`Dropped queue ${this.name}`);
    });
  }

  /**
   * Mark request as handled (no-op for simple queue)
   */
  async markRequestHandled(request) {
    // No-op for simple queue since we already removed it in fetchNextRequest
    log.debug(`Request marked as handled: ${request.url}`);
  }
}

// Global queue instance
let queue;

/**
 * Get or initialize simple queue
 */
export async function getOrInitSimpleQueue(makeFresh = false) {
  if (queue && !makeFresh) {
    return queue;
  }

  const queueName = process.env.QUEUE_NAME || "manga-crawler-queue";
  queue = new SimpleQueue(queueName);

  if (makeFresh) {
    await queue.drop();
  }

  log.info(`📊 Simple queue '${queueName}' initialized`);
  return queue;
}

/**
 * Get simple queue stats
 */
export async function getSimpleQueueStats() {
  if (!queue) {
    return { size: 0, isEmpty: true, isInitialized: false };
  }

  try {
    const size = await queue.size();
    const isEmpty = await queue.isEmpty();
    
    return {
      size,
      isEmpty,
      isInitialized: true,
      name: queue.name
    };
  } catch (error) {
    log.error("❌ Failed to get simple queue stats:", error);
    return { size: 0, isEmpty: true, isInitialized: false, error: error.message };
  }
}
