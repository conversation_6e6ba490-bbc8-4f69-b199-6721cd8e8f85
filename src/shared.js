import { RequestQueue, Configuration, log } from "crawlee";

// Create the request queue that also supports parallelization
let queue;
let isInitializing = false;

/**
 * Enhanced queue initialization with better error handling and configuration
 * @param {boolean} makeFresh Whether the queue should be cleared before returning it
 * @returns {Promise<RequestQueue>} The queue instance
 */
export async function getOrInitQueue(makeFresh = false) {
  // Prevent concurrent initialization
  if (isInitializing) {
    while (isInitializing) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    return queue;
  }

  if (queue && !makeFresh) {
    return queue;
  }

  isInitializing = true;

  try {
    const queueName = process.env.QUEUE_NAME || "manga-crawler-queue";

    log.debug(`🔄 ${makeFresh ? 'Recreating' : 'Opening'} queue: ${queueName}`);

    if (makeFresh && queue) {
      try {
        await queue.drop();
        log.info("🗑️ Dropped existing queue");
      } catch (error) {
        log.warn("⚠️ Failed to drop existing queue:", error.message);
      }
      queue = null;
    }

    // Simple approach - just open the queue with default settings
    queue = await RequestQueue.open(queueName);

    try {
      const queueSize = await queue.size();
      log.info(`📊 Queue '${queueName}' initialized with ${queueSize} requests`);
    } catch (sizeError) {
      log.info(`📊 Queue '${queueName}' initialized (size check failed: ${sizeError.message})`);
    }

    return queue;
  } catch (error) {
    log.error("❌ Failed to initialize queue:", error);
    throw error;
  } finally {
    isInitializing = false;
  }
}

/**
 * Get queue statistics
 * @returns {Promise<Object>} Queue statistics
 */
export async function getQueueStats() {
  if (!queue) {
    return { size: 0, isEmpty: true, isInitialized: false };
  }

  try {
    let size = 0;
    let isEmpty = true;

    // For RequestQueue, we need to check differently
    isEmpty = await queue.isEmpty();

    // Try to estimate size by checking if we can fetch a request
    try {
      const testRequest = await queue.fetchNextRequest();
      if (testRequest) {
        // Put it back by re-adding it
        await queue.addRequest(testRequest);
        size = 1; // At least 1 request
        isEmpty = false;
      } else {
        size = 0;
        isEmpty = true;
      }
    } catch (error) {
      // If we can't fetch, assume empty
      size = 0;
      isEmpty = true;
    }

    return {
      size,
      isEmpty,
      isInitialized: true,
      name: queue.name || 'unknown'
    };
  } catch (error) {
    log.error("❌ Failed to get queue stats:", error);
    return { size: 0, isEmpty: true, isInitialized: false, error: error.message };
  }
}

/**
 * Safely add requests to queue with retry logic
 * @param {Array|Object} requests Request(s) to add
 * @param {Object} options Options for adding requests
 * @returns {Promise<number>} Number of requests successfully added
 */
export async function safeAddRequests(requests, options = {}) {
  const { retries = 3, retryDelay = 1000 } = options;
  const requestArray = Array.isArray(requests) ? requests : [requests];

  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      const sharedQueue = await getOrInitQueue(false);
      await sharedQueue.addRequests(requestArray);

      log.debug(`✅ Added ${requestArray.length} requests to queue (attempt ${attempt})`);
      return requestArray.length;
    } catch (error) {
      log.warn(`⚠️ Failed to add requests (attempt ${attempt}/${retries}):`, error.message);

      if (attempt === retries) {
        log.error(`❌ Failed to add requests after ${retries} attempts:`, error);
        throw error;
      }

      await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));
    }
  }

  return 0;
}
