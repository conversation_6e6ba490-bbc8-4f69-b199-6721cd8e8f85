import { Request } from "crawlee";
import { extractNumberFromString } from "../utils/index.js";
import { extractAndEnqueueRequests } from "../utils/url-helpers.js";
import { getOrInitQueueAdapter } from "../queue-adapter.js";

export async function handleMangaList(context) {
  const { parseWithCheerio, log, request, enqueueLinks, addRequests } = context;
  const sharedQueue = await getOrInitQueueAdapter();
  const { site } = request.userData;

  // Ensure log has all required methods
  const logger = {
    info: log.info?.bind(log) || console.log,
    warn: log.warn?.bind(log) || console.warn,
    error: log.error?.bind(log) || console.error,
    debug: log.debug?.bind(log) || console.log,
  };

  if (!site) {
    logger.error("🐞 No site information found in request userData.");
    return;
  }

  logger.info(`🔎 Scraping ${request.url}`);

  let currentPage = 1;

  const $ = await parseWithCheerio();

  // Enqueue manga detail links from current page
  const mangaLinksCount = $(site.selectors.listManga.mangaLink).length;
  logger.info(`📚 Found ${mangaLinksCount} manga links on current page`);

  if (mangaLinksCount > 0) {
    logger.info(`🔗 Attempting to enqueue ${mangaLinksCount} MANGA_DETAIL requests...`);

    const addedCount = await extractAndEnqueueRequests({
      $,
      selector: site.selectors.listManga.mangaLink,
      baseUrl: request.url,
      label: "MANGA_DETAIL",
      userData: { site },
      addRequests,
      sharedQueue,
      log: logger
    });

    if (addedCount === 0) {
      logger.warn(`⚠️ No valid manga URLs could be extracted from ${mangaLinksCount} links`);
    }
  } else {
    logger.warn(`⚠️ No manga links found with selector: ${site.selectors.listManga.mangaLink}`);
  }

  const $nextPage = $(site.selectors.listManga.nextPage);
  const $lastPage = $(site.selectors.listManga.lastPage);

  if ($lastPage) {
    const lastPageNumber = extractNumberFromString($lastPage.attr("href"));
    logger.info(`Last page number: ${lastPageNumber}`);

    const nextPageUrls = [];

    for (let i = currentPage + 1; i <= site.maxPages; i++) {
      if (i > lastPageNumber) {
        logger.info(`Skipping page ${i} as it exceeds the last page number.`);
        continue;
      }
      const pageUrl = site.listPageUrl.replace("$page", String(i));
      const url = `${site.baseUrl}${pageUrl}`;

      nextPageUrls.push(
        new Request({
          url,
          userData: { site, label: "MANGA_LIST" },
          label: "MANGA_LIST",
        }),
      );
    }

    logger.info(`📄 Adding ${nextPageUrls.length} additional pages to queue`);

    try {
      await sharedQueue.addRequests(nextPageUrls);
      logger.info(`✅ Successfully added ${nextPageUrls.length} MANGA_LIST requests`);
    } catch (error) {
      logger.error(`❌ Failed to add next page URLs:`, error);
      // Continue processing even if we can't add next pages
    }
    return;
  }
  if ($nextPage.length > 0 && currentPage <= site.maxPages) {
    currentPage++;
    // For simple queue, we need to manually extract and add URLs
    try {
      const nextPageUrls = [];
      $(site.selectors.listManga.nextPage).each((_, element) => {
        const href = $(element).attr('href');
        if (href) {
          const fullUrl = href.startsWith('http') ? href : `${site.baseUrl}${href}`;
          nextPageUrls.push(new Request({
            url: fullUrl,
            label: "MANGA_LIST",
            userData: { site },
          }));
        }
      });

      if (nextPageUrls.length > 0) {
        await sharedQueue.addRequests(nextPageUrls);
        logger.info(`✅ Added ${nextPageUrls.length} next page requests`);
      }
    } catch (error) {
      logger.error(`❌ Failed to add next page requests:`, error);
    }
  }
}
