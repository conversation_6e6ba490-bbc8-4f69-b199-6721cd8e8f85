import * as extractors from "../extractors/index.js";
import { extractAndEnqueueRequests } from "../utils/url-helpers.js";
import { getOrInitQueueAdapter } from "../queue-adapter.js";

export async function handleMangaDetail({
  parseWithCheerio,
  log,
  request,
  addRequests,
}) {
  const { site } = request.userData;

  if (!site) {
    log.error("🐞 No site information found in request userData.");
    return;
  }

  log.info(`👉 Scraping detail page ${request.url}`);

  try {
    const $ = await parseWithCheerio();
    const sharedQueue = await getOrInitQueueAdapter();

    const mangaDetail = extractors.mangaDetail($, site?.selectors?.detailManga);
    log.info(`✅ Scraped ${mangaDetail?.title || 'Unknown Title'}`);

    // Enqueue chapter links
    const chapterLinksCount = $(site.selectors.detailManga.chapterLink).length;
    log.info(`📖 Found ${chapterLinksCount} chapter links for manga: ${mangaDetail?.title || 'Unknown'}`);

    if (chapterLinksCount > 0) {
      const addedCount = await extractAndEnqueueRequests({
        $,
        selector: site.selectors.detailManga.chapterLink,
        baseUrl: request.url,
        label: "CHAPTER_DETAIL",
        userData: { site, mangaTitle: mangaDetail?.title },
        addRequests,
        sharedQueue,
        log
      });

      if (addedCount === 0) {
        log.warn(`⚠️ No valid chapter URLs could be extracted from ${chapterLinksCount} links`);
      }
    } else {
      log.warn(`⚠️ No chapter links found with selector: ${site.selectors.detailManga.chapterLink}`);
    }

    // Send manga data to parent process
    if (mangaDetail && Object.keys(mangaDetail).length > 0) {
      process.send?.({
        type: 'data',
        payload: {
          ...mangaDetail,
          url: request.url,
          site: site.name || site.baseUrl,
          scrapedAt: new Date().toISOString(),
          type: 'manga'
        }
      });
    }
  } catch (error) {
    log.error(`❌ Error scraping manga detail page ${request.url}:`, error);
    throw error;
  }
}
