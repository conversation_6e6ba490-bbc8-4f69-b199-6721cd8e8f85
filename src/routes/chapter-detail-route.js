import * as extractors from "../extractors/index.js";

export async function handleChapterDetail({
  parseWithCheerio,
  log,
  request,
  waitForSelector,
}) {
  const { site, mangaTitle } = request?.userData || {};

  if (!site) {
    log.error("🐞 No site information found in request userData.");
    return;
  }

  log.info(`🔎 Scraping chapter link ${request.url}`);

  try {
    const $ = await parseWithCheerio();
    const chapterDetailSelectors = site?.selectors?.detailChapter;

    // Wait for content to load if selector is provided
    if (chapterDetailSelectors?.waitForSelector) {
      try {
        await waitForSelector(chapterDetailSelectors.waitForSelector);
      } catch (error) {
        log.warn(`⚠️ Wait for selector failed: ${chapterDetailSelectors.waitForSelector}`, error);
      }
    }

    const chapterDetail = extractors.chapterDetail(
      $,
      site?.selectors?.detailChapter,
      site,
    );

    const chapterTitle = chapterDetail?.title || 'Unknown Chapter';
    const pageCount = chapterDetail?.pages?.length || 0;

    log.info(`🇻🇳 Scraped Chapter ${chapterTitle}`);
    log.info(`🌼 Scraped Pages ${pageCount} pages`);

    // Send chapter data to parent process
    if (chapterDetail && Object.keys(chapterDetail).length > 0) {
      process.send?.({
        type: 'data',
        payload: {
          ...chapterDetail,
          url: request.url,
          mangaTitle: mangaTitle || 'Unknown Manga',
          site: site.name || site.baseUrl,
          scrapedAt: new Date().toISOString(),
          type: 'chapter'
        }
      });
    }
  } catch (error) {
    log.error(`❌ Error scraping chapter detail page ${request.url}:`, error);
    throw error;
  }
}
