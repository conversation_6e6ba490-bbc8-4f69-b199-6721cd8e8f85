import { Request, tryAbsoluteURL } from "crawlee";

/**
 * Extract URLs from elements and create Request objects
 * @param {CheerioAPI} $ - Cheerio instance
 * @param {string} selector - CSS selector for links
 * @param {string} baseUrl - Base URL for resolving relative URLs
 * @param {string} label - Request label
 * @param {object} userData - User data to attach to requests
 * @param {function} log - Logger function
 * @returns {Request[]} Array of Request objects
 */
export function extractRequestsFromLinks(
  $,
  selector,
  baseUrl,
  label,
  userData,
  logger
) {
  const requests = [];
  const seenUrls = new Set(); // Prevent duplicates

  try {
    const elements = $(selector);
    logger?.debug(`🔍 Found ${elements.length} elements matching selector: ${selector}`);

    elements.each((index, element) => {
      try {
        const href = $(element).attr("href");
        if (!href) {
          logger?.debug(`⚠️ Element ${index} has no href attribute`);
          return;
        }

        const fullUrl = tryAbsoluteURL(href, baseUrl);
        if (!fullUrl) {
          logger?.debug(`⚠️ Could not resolve URL: ${href} with base: ${baseUrl}`);
          return;
        }

        // Check for duplicates
        if (seenUrls.has(fullUrl)) {
          logger?.debug(`🔄 Skipping duplicate URL: ${fullUrl}`);
          return;
        }
        seenUrls.add(fullUrl);

        // Validate URL format
        try {
          new URL(fullUrl);
        } catch (urlError) {
          logger?.warn(`❌ Invalid URL format: ${fullUrl}`);
          return;
        }

        requests.push(
          new Request({
            url: fullUrl,
            label,
            userData: { ...userData }, // Clone to prevent mutations
            uniqueKey: `${label}:${fullUrl}`, // Ensure uniqueness
          })
        );

        logger?.debug(`🔗 Prepared ${label}: ${fullUrl}`);
      } catch (error) {
        logger?.error(`❌ Error processing element ${index}:`, error);
      }
    });

    logger?.info(`✅ Extracted ${requests.length} unique URLs from ${elements.length} elements`);
  } catch (error) {
    logger?.error(`❌ Error extracting requests from selector ${selector}:`, error);
  }

  return requests;
}

/**
 * Extract and enqueue requests using safe shared queue with retry logic
 * @param {object} params - Parameters object
 * @param {CheerioAPI} params.$ - Cheerio instance
 * @param {string} params.selector - CSS selector for links
 * @param {string} params.baseUrl - Base URL for resolving relative URLs
 * @param {string} params.label - Request label
 * @param {object} params.userData - User data to attach to requests
 * @param {function} params.addRequests - addRequests function from context (fallback)
 * @param {object} params.sharedQueue - Shared queue instance (optional)
 * @param {function} params.log - Logger function
 * @returns {Promise<number>} Number of requests added
 */
export async function extractAndEnqueueRequests({
  $,
  selector,
  baseUrl,
  label,
  userData,
  addRequests,
  sharedQueue,
  log,
}) {
  try {
    const requests = extractRequestsFromLinks(
      $,
      selector,
      baseUrl,
      label,
      userData,
      log
    );

    if (requests.length === 0) {
      log?.warn(`⚠️ No valid requests extracted from selector: ${selector}`);
      return 0;
    }

    // Use shared queue if available
    if (sharedQueue) {
      try {
        await sharedQueue.addRequests(requests);
        log?.info(`✅ Added ${requests.length} ${label} requests using shared queue`);
        return requests.length;
      } catch (error) {
        log?.error(`❌ Failed to add requests to shared queue, falling back to addRequests:`, error);
        // Fallback to addRequests
        await addRequests(requests);
        log?.info(`✅ Added ${requests.length} ${label} requests using addRequests fallback`);
        return requests.length;
      }
    } else {
      // Direct fallback to addRequests
      await addRequests(requests);
      log?.info(`✅ Added ${requests.length} ${label} requests using addRequests`);
      return requests.length;
    }
  } catch (error) {
    log?.error(`❌ Error in extractAndEnqueueRequests:`, error);
    throw error;
  }
}
