import { fork } from "node:child_process";

import {
  Configuration,
  Dataset,
  AdaptivePlaywrightCrawler,
  log,
} from "crawlee";

import {
  BrowserName,
  DeviceCategory,
  OperatingSystemsName,
} from "@crawlee/browser-pool";

import { launchOptions } from 'camoufox-js';
import { firefox } from 'playwright';

import { router } from "./routes/index.js";
import { getOrInitQueue, getQueueStats } from "./shared.js";
import { sites } from "./sites/index.js";
import { config, getWorkerCount, getWorkerStorageDir, validateConfig, logConfig } from "./config.js";

// Validate configuration on startup
if (!validateConfig()) {
  process.exit(1);
}

const initSites = async () => {
  const requestQueue = await getOrInitQueue(true);

  log.info(`🌱 Initializing ${sites.filter(s => s.enabled).length} enabled sites`);

  for (const site of sites) {
    if (!site.enabled) {
      log.debug(`⏭️ Skipping disabled site: ${site.name || site.baseUrl}`);
      continue;
    }

    try {
      await requestQueue.addRequest({
        url: `${site.baseUrl}${site.startPath}`,
        userData: { site },
        label: "MANGA_LIST",
      });
      log.info(`✅ Added initial URL for site: ${site.name || site.baseUrl}`);
    } catch (error) {
      log.error(`❌ Failed to add initial URL for site ${site.name || site.baseUrl}:`, error);
    }
  }

  try {
    const queueStats = await getQueueStats();
    log.info(`📊 Queue initialized with ${queueStats.size} requests`);
  } catch (error) {
    log.warn(`⚠️ Could not get queue size:`, error.message);
  }
};

if (!process.env.IN_WORKER_THREAD) {
  // Main process - orchestrates workers
  logConfig();

  const workerCount = getWorkerCount();

  log.info(`🚀 Setting up ${workerCount} worker threads`);

  // Initialize sites before starting workers
  await initSites();

  // Wait a bit to ensure queue is properly populated
  await new Promise(resolve => setTimeout(resolve, 1000));

  // Log initial queue stats
  const queueStats = await getQueueStats();
  log.info(`📊 Initial queue stats:`, queueStats);

  // Verify we have requests before starting workers
  if (queueStats.size === 0 && queueStats.isEmpty) {
    log.error("❌ No requests in queue! Cannot start workers.");
    process.exit(1);
  }

  const currentFile = new URL(import.meta.url).pathname;
  const workers = [];
  const promises = [];

  // Graceful shutdown handler
  const shutdown = async () => {
    log.info("🛑 Graceful shutdown initiated...");

    for (const worker of workers) {
      if (worker.proc && !worker.proc.killed) {
        worker.proc.kill('SIGTERM');
      }
    }

    // Wait for workers to finish or timeout
    await Promise.race([
      Promise.all(promises),
      new Promise(resolve => setTimeout(resolve, 10000)) // 10s timeout
    ]);

    log.info("✅ Shutdown complete");
    process.exit(0);
  };

  process.on('SIGINT', shutdown);
  process.on('SIGTERM', shutdown);

  for (let i = 0; i < workerCount; i++) {
    const worker = { id: i, proc: null, startTime: Date.now() };

    worker.proc = fork(currentFile, {
      env: {
        ...process.env,
        IN_WORKER_THREAD: "true",
        WORKER_INDEX: String(i),
      },
      silent: false, // Allow direct stdout/stderr
    });

    workers.push(worker);

    worker.proc.on("online", () => {
      log.info(`🟢 Worker ${i} is online (PID: ${worker.proc.pid})`);
    });

    worker.proc.on("message", async (data) => {
      try {
        if (data.type === 'data') {
          await Dataset.pushData(data.payload);
          log.debug(`📊 Worker ${i} sent data`);
        } else if (data.type === 'stats') {
          log.info(`📈 Worker ${i} stats:`, data.payload);
        }
      } catch (error) {
        log.error(`❌ Failed to process message from worker ${i}:`, error);
      }
    });

    worker.proc.on("error", (error) => {
      log.error(`❌ Worker ${i} error:`, error);
    });

    promises.push(
      new Promise((resolve) => {
        worker.proc.once("exit", (code, signal) => {
          const runtime = Date.now() - worker.startTime;
          log.info(`🔴 Worker ${i} exited with code ${code} and signal ${signal} (runtime: ${Math.round(runtime/1000)}s)`);
          resolve({ workerId: i, code, signal, runtime });
        });
      })
    );
  }

  const results = await Promise.all(promises);

  // Log final statistics
  const successfulWorkers = results.filter(r => r.code === 0).length;
  const totalRuntime = Math.max(...results.map(r => r.runtime));

  log.info(`🎉 Crawling complete! ${successfulWorkers}/${workerCount} workers successful (total runtime: ${Math.round(totalRuntime/1000)}s)`);
} else {
  // Worker process
  const workerId = process.env.WORKER_INDEX;
  const workerLogger = log.child({
    prefix: `[Worker ${workerId}]`,
  });

  // Set log level based on environment
  const logLevel = process.env.LOG_LEVEL || 'INFO';
  workerLogger.setLevel(log.LEVELS[logLevel]);

  workerLogger.info("🔧 Initializing worker...");

  // Disable purge on start to preserve shared queue
  Configuration.set("purgeOnStart", false);

  // Use shared storage for all workers to ensure queue sharing
  const crawlerConfig = new Configuration({
    storageClientOptions: {
      localDataDirectory: './storage/shared',
    },
  });

  // Get shared request queue (don't recreate, use existing)
  const requestQueue = await getOrInitQueue(false);

  // Verify queue has requests
  const initialStats = await getQueueStats();
  workerLogger.info(`📊 Worker queue stats: size=${initialStats.size}, isEmpty=${initialStats.isEmpty}`);

  // Worker statistics
  let requestsProcessed = 0;
  let requestsFailed = 0;
  const startTime = Date.now();

  // Send stats periodically
  const statsInterval = setInterval(async () => {
    const runtime = Date.now() - startTime;
    const queueStats = await getQueueStats();
    const stats = {
      requestsProcessed,
      requestsFailed,
      runtime: Math.round(runtime / 1000),
      queueSize: queueStats.size,
      memoryUsage: Math.round(process.memoryUsage().heapUsed / 1024 / 1024), // MB
    };

    process.send?.({
      type: 'stats',
      payload: stats
    });
  }, config.workers.statsIntervalMs);

  // Graceful shutdown handler
  const workerShutdown = async () => {
    workerLogger.info("🛑 Worker shutdown initiated...");
    clearInterval(statsInterval);

    if (crawler) {
      await crawler.teardown();
    }

    workerLogger.info("✅ Worker shutdown complete");
    process.exit(0);
  };

  process.on('SIGTERM', workerShutdown);
  process.on('SIGINT', workerShutdown);

  workerLogger.debug("🕷️ Setting up crawler...");

  const crawler = new AdaptivePlaywrightCrawler(
    {
      browserPoolOptions: {
        useFingerprints: false,
        fingerprintOptions: {
          fingerprintGeneratorOptions: {
            browsers: [
              {
                name: BrowserName.edge,
                minVersion: 96,
              },
            ],
            devices: [DeviceCategory.desktop],
            operatingSystems: [OperatingSystemsName.windows],
          },
        },
      },
      postNavigationHooks: [
        async ({ handleCloudflareChallenge }) => {
          await handleCloudflareChallenge();
        },
      ],
      launchContext: {
        launcher: firefox,
        launchOptions: await launchOptions({
          headless: config.crawler.headless,
        }),
      },
      renderingTypeDetectionRatio: 0.1,
      log: workerLogger,
      requestHandler: async (context) => {
        try {
          requestsProcessed++;
          await router(context);
        } catch (error) {
          requestsFailed++;
          workerLogger.error(`❌ Request failed: ${context.request.url}`, error);
          throw error;
        }
      },
      // Note: Using standard RequestQueue for better compatibility
      // experiments: {
      //   requestLocking: true,
      // },
      requestQueue,
      maxConcurrency: config.workers.maxConcurrencyPerWorker,
      maxRequestRetries: config.crawler.maxRetries,
      requestHandlerTimeoutSecs: config.crawler.requestTimeoutSecs,
      navigationTimeoutSecs: config.crawler.navigationTimeoutSecs,
    },
    crawlerConfig
  );

  workerLogger.info("🚀 Starting crawler...");

  try {
    await crawler.run();

    // Send final stats
    const finalStats = {
      requestsProcessed,
      requestsFailed,
      runtime: Math.round((Date.now() - startTime) / 1000),
      status: 'completed'
    };

    process.send?.({
      type: 'stats',
      payload: finalStats
    });

    workerLogger.info(`✅ Crawler completed. Processed: ${requestsProcessed}, Failed: ${requestsFailed}`);
  } catch (error) {
    workerLogger.error("💥 Crawler failed:", error);
    process.exit(1);
  } finally {
    clearInterval(statsInterval);
  }
}
